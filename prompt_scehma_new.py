assets_schema = {
  "current_assets": {
    "asset_type": [
      {"name": "cash_and_cash_equivalents", "value": ""},
      {"name": "inventory", "value": ""},
      {"name": "other_assets", "value": ""},
      {"name": "trade_receivable", "value": ""}
    ]
  },
  "date": "",
  "other_non_current_assets": {
    "asset_type": [
      {"name": "capital_work_in_progress", "value": ""},
      {"name": "intangible_assets", "value": ""},
      {"name": "financial_assets", "value": ""},
      {"name": "other_non_current_assets", "value": ""},
      {"name": "income_tax_assets_net", "value": ""}
    ]
  },
  "property_plant_equipment": {
    "asset_type": [
      {"name": "property_plant_equipment", "value": ""}
    ]
  }
}

assets_prompt = f"""
Please extract asset-related data for the company using the schema provided below.

**Important Instructions**:
1. Extract only the **company-specific data**. Ignore group or consolidated values.
2. If both company and group data are available, use **only the company** data.
3. If any of the values are presented in **thousands, lakhs, crores, millions, billions, or trillions**, convert them to **whole numbers** using the appropriate multiplier. Refer to the column heading or section title in the image to determine the unit. Use the following conversions:

        - thousand = 1,000  
        - lakh = 100,000  
        - crore = 10,000,000  
        - million = 1,000,000  
        - billion = 1,000,000,000  
        - trillion = 1,000,000,000,000  

        For example:
        - 23 million → 23000000  
        - 15 billion → 15000000000  
        - 25 crore → *********
        - 4.5L → 450000  
        - 1.2B → 1200000000

      Convert all such values into plain whole numbers with no suffixes.4. If the data for a specific year has been restated or revised, that year's data should be excluded from extraction.
5. Output must strictly follow the **JSON structure provided below**, keeping all field names and hierarchy intact.
6. Should not omit any keys—if data is unavailable, use an empty string ("") or null as appropriate, and do not infer or assume values.Please return the empty schema as it is provided in the prompt.if the data is not available(Please do not forget this point).
7. All "value" fields must be returned as **numbers**, not strings. if a value is unavailable, use null — do not use empty strings or placeholder text.
8. In the date field, return only in this format DD/MM/YYYY (eg: 31/12/2024 for the 31st of December 2024).
Here is the schema you must follow:
{assets_schema}
"""

cashflows_schema = {
  "cashflows": [
    {
      "capex": "",
      "cash_from_operations": "",
      "free_cash_flow": "",
      "year": ""
    }
  ]
}

cashflows_prompt = f"""
Please extract capital expenditure and related data for the **company** based on the schema provided below.

**Important Instructions**:
1. Extract only the **company-specific data**. Ignore group or consolidated data.
2. If both company and group data are available, use **only the company** data.
3. If any of the values are presented in **thousands, lakhs, crores, millions, billions, or trillions**, convert them to **whole numbers** using the appropriate multiplier. Refer to the column heading or section title in the image to determine the unit. Use the following conversions:

        - thousand = 1,000  
        - lakh = 100,000  
        - crore = 10,000,000  
        - million = 1,000,000  
        - billion = 1,000,000,000  
        - trillion = 1,000,000,000,000  

        For example:
        - 23 million → 23000000  
        - 15 billion → 15000000000  
        - 25 crore → *********
        - 4.5L → 450000  
        - 1.2B → 1200000000

        Convert all such values into plain whole numbers with no suffixes.

4. If data for two different years is provided, extract and return both years' data, formatted according to the specified schema for each year.
   However, if the data for a particular year is clearly marked as restated or revised, that year's data should be excluded from extraction.
   If the data is direct (i.e., not restated or revised), it can be extracted.
5. Please return the **JSON structure** provided below as it is provided, keeping all field names and hierarchy intact.if the data is not available(Please do not forget this point).
6. Return only the year value from the date — ignore the month and day.
7. All "value" fields must be returned as **numbers**, not strings. f a value is unavailable, use null — do not use empty strings or placeholder text.
8. you should not put the - (negative sighn) in the value field.

Here is the schema you must follow:
{cashflows_schema}
"""
schema_revenue_expenses = {
  "revenue_expenses": [
    {
      "expenses": {
        "corporate_tax": "",
        "depreciation": "",
        "exceptions_before_tax": "",
        "fuel_expenses": "",
        "interest": "",
        "o_m_expenses": ""
      },
      "revenue": {
        "other_income": "",
        "revenue_operations": ""
      },
      "year": ""
    }
  ]
}

revenue_expenses_prompt = f"""
Please extract revenue and expense details for the **company** using the schema provided below.

**Important Instructions**:
1. Extract only the **company-specific data**. Ignore any group or consolidated values.
2. If both company and group data are available, use **only the company** data.
3. If any of the values are presented in **thousands, lakhs, crores, millions, billions, or trillions**, convert them to **whole numbers** using the appropriate multiplier. Refer to the column heading or section title in the image to determine the unit. Use the following conversions:

        - thousand = 1,000  
        - lakh = 100,000  
        - crore = 10,000,000  
        - million = 1,000,000  
        - billion = 1,000,000,000  
        - trillion = 1,000,000,000,000  

        For example:
        - 23 million → 23000000  
        - 15 billion → 15000000000  
        - 25 crore → *********
        - 4.5L → 450000  
        - 1.2B → 1200000000

      Convert all such values into plain whole numbers with no suffixes.
4. If data for two different years is provided, extract and return both years' data, formatted according to the specified schema for each year.
   However, if the data for a particular year is clearly marked as restated or revised, that year's data should be excluded from extraction.
   If the data is direct (i.e., not restated or revised), it can be extracted.
   6. Return only the year value from the date — ignore the month and day.
5. Please return the **JSON structure** provided below as it is provided, keeping all field names and hierarchy intact.if the data is not available(Please do not forget this point).
6.Return only the year value from the date — ignore the month and day
7.All "value" fields must be returned as **numbers**, not strings. f a value is unavailable, use null — do not use empty strings or placeholder text.
8. Do not put the - (negative sighn) in the value field.

Here is the schema you must follow:
{schema_revenue_expenses}
"""

credit_rating_schema = {
  "credit_rating": [
    {
      "agency": "",
      "name": "",
      "yearwise_rating": [
        {"rating": "", "rating_trunc": "", "year": ""},
      ]
    }
  ],
  "credit_rating_note": "",
  "currency": ""
}

credit_rating_prompt = f"""
Your task is to extract the **year-wise credit rating** for each credit rating agency based on the column titled **"Original Credit Rating / Outlook"**.

**Instructions**:
1. Use the instrument with the **highest rated amount** or **most significant exposure** per agency when multiple instruments are listed.
2. For the `credit_rating_note`, extract the instrument type on which the rating is based.  
   For example, if ratings are based on a **Long Term Loan Facility**, then the note should be:  
   `"Credit ratings displayed below are for Long Term Loan Facility."`
3. Extract the **currency** mentioned in the credit rating section (e.g., INR, USD, etc.).
4. Should not omit any keys—if data is unavailable, use an empty string ("") or null as appropriate, and do not infer or assume values.please return the empty schema as it is provided in the prompt if the data is not available.
5. Return only the year value from the date — ignore the month and day
6.Extract year-wise credit ratings exactly as per the schema; if any field is missing or unavailable, set its value to an empty string or null, do not guess, assume, You should not use placeholder values like "Agency Name" or "AAA", and return only the empty string if the data is not available.

Here is the schema you must follow:
{credit_rating_schema}
"""